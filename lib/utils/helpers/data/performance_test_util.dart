import 'dart:async';
import 'package:flutter/material.dart';

import 'package:logger/logger.dart';
import '../../../core/gmail/offline_mode/gmail_offline_mode.dart' as offline;

/// Utility class to perform performance tests on the app.
///
/// Provides methods to:
/// - Test data loading performance
/// - Test email list rendering performance
/// - Test cache effectiveness
class PerformanceTestUtil {
  static final Logger _logger = Logger();
  final Map<String, dynamic> results = {}; // Placeholder for results map

  /// Tests the performance of data loading for a given user.
  ///
  /// [userEmail] - The email address of the user whose data to load.
  /// [iterations] - Number of times to repeat the test (default: 3).
  /// Returns a map with average, min, max load times and memory usage.
  static Future<Map<String, dynamic>> testDataLoading({
    required String userEmail,
    int iterations = 3,
  }) async {
    final results = <String, dynamic>{};
    final loadTimes = <int>[];
    final memorySizes = <int>[];

    _logger.i('Starting performance test for data loading');

    for (int i = 0; i < iterations; i++) {
      _logger.i('Iteration [34m${i + 1}/$iterations[0m');

      // Clear caches to ensure fresh load
      offline.GmailCacheManager.invalidateCountCache(userEmail);
      offline.GmailCacheManager.invalidateSpamEmailsCache(userEmail);

      final stopwatch = Stopwatch()..start();

      // Load grouped data
      try {
        await offline.GmailDataService.getSortedAndGroupedEmails(userEmail);
        stopwatch.stop();
        loadTimes.add(stopwatch.elapsedMilliseconds);
        _logger.i('Load time: ${stopwatch.elapsedMilliseconds}ms');

        // Estimate memory usage (placeholder - implement real memory tracking if possible)
        final memoryUsage = await _estimateMemoryUsage();
        memorySizes.add(memoryUsage);

        // Wait between iterations
        await Future.delayed(const Duration(seconds: 1));
      } catch (e) {
        _logger.e('Error during test: $e');
      }
    }

    // Calculate average metrics
    final avgLoadTime =
        loadTimes.isNotEmpty
            ? loadTimes.reduce((a, b) => a + b) / loadTimes.length
            : 0;

    final avgMemory =
        memorySizes.isNotEmpty
            ? memorySizes.reduce((a, b) => a + b) / memorySizes.length
            : 0;

    results['averageLoadTimeMs'] = avgLoadTime;
    results['minimumLoadTimeMs'] =
        loadTimes.isNotEmpty ? loadTimes.reduce((a, b) => a < b ? a : b) : 0;
    results['maximumLoadTimeMs'] =
        loadTimes.isNotEmpty ? loadTimes.reduce((a, b) => a > b ? a : b) : 0;
    results['averageMemoryUsageKB'] = avgMemory / 1024;
    results['iterations'] = iterations;
    results['rawResults'] = {
      'loadTimes': loadTimes,
      'memorySizes': memorySizes,
    };

    _logger.i('Performance test completed: $results');
    return results;
  }

  /// Tests rendering performance of the email list.
  ///
  /// [emails] - List of email maps to render.
  /// [context] - BuildContext for rendering.
  /// [scrollTestDurationSeconds] - Duration for scroll test (default: 5).
  /// Returns a map with initial render time and email count.
  static Future<Map<String, dynamic>> testEmailRendering({
    required List<Map<String, dynamic>> emails,
    required BuildContext context,
    int scrollTestDurationSeconds = 5,
  }) async {
    final results = <String, dynamic>{};
    final renderTimes = <int>[];

    _logger.i('Starting performance test for email rendering');

    // Test initial render time
    final stopwatch = Stopwatch()..start();

    // Force layout and render cycles
    await Future.delayed(const Duration(milliseconds: 100));
    stopwatch.stop();

    renderTimes.add(stopwatch.elapsedMilliseconds);

    results['initialRenderTimeMs'] = stopwatch.elapsedMilliseconds;
    results['emailCount'] = emails.length;

    _logger.i(
      'Initial render time: ${stopwatch.elapsedMilliseconds}ms for ${emails.length} emails',
    );

    return results;
  }

  /// Tests cache effectiveness for grouped email data.
  ///
  /// [userEmail] - The email address of the user whose data to test.
  /// [iterations] - Number of times to repeat the test (default: 5).
  /// Returns a map with average uncached/cached times and speedup factor.
  static Future<Map<String, dynamic>> testCacheEffectiveness({
    required String userEmail,
    int iterations = 5,
  }) async {
    final results = <String, dynamic>{};
    final uncachedTimes = <int>[];
    final cachedTimes = <int>[];

    _logger.i('Starting performance test for cache effectiveness');

    // First, clear caches
    offline.GmailCacheManager.invalidateCountCache(userEmail);
    offline.GmailCacheManager.invalidateSpamEmailsCache(userEmail);

    // Test uncached performance
    for (int i = 0; i < iterations; i++) {
      if (i > 0) {
        // Clear cache between iterations, except first which is already cleared
        offline.GmailCacheManager.invalidateCountCache(userEmail);
        offline.GmailCacheManager.invalidateSpamEmailsCache(userEmail);
      }

      final stopwatch = Stopwatch()..start();
      await offline.GmailDataService.getSortedAndGroupedEmails(userEmail);
      stopwatch.stop();

      uncachedTimes.add(stopwatch.elapsedMilliseconds);
    }

    // Test cached performance
    for (int i = 0; i < iterations; i++) {
      // Don't clear cache between iterations to test cache hits
      final stopwatch = Stopwatch()..start();
      await offline.GmailDataService.getSortedAndGroupedEmails(userEmail);
      stopwatch.stop();

      cachedTimes.add(stopwatch.elapsedMilliseconds);
    }

    final avgUncachedTime =
        uncachedTimes.isNotEmpty
            ? uncachedTimes.reduce((a, b) => a + b) / uncachedTimes.length
            : 0;

    final avgCachedTime =
        cachedTimes.isNotEmpty
            ? cachedTimes.reduce((a, b) => a + b) / cachedTimes.length
            : 0;

    final speedupFactor =
        avgUncachedTime > 0 ? avgUncachedTime / avgCachedTime : 0;

    results['averageUncachedTimeMs'] = avgUncachedTime;
    results['averageCachedTimeMs'] = avgCachedTime;
    results['speedupFactor'] = speedupFactor;
    results['cacheEffectiveness'] =
        (1 - (avgCachedTime / avgUncachedTime)) * 100;
    results['rawResults'] = {
      'uncachedTimes': uncachedTimes,
      'cachedTimes': cachedTimes,
    };

    _logger.i('Cache test completed: $results');
    return results;
  }

  /// Estimates memory usage (placeholder implementation).
  /// In a real implementation, use platform-specific methods to get actual memory usage.
  static Future<int> _estimateMemoryUsage() async {
    // This is a placeholder. In a real implementation, you might use
    // platform-specific methods to get actual memory usage.
    return 10000000; // 10MB placeholder
  }
}
