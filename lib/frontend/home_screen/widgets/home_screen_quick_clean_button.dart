import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:provider/provider.dart';
import '../../../providers/user_provider.dart';
import '../../../core/gmail/online_mode/gmail_online_mode.dart';
import '../../../core/gmx/online_mode/gmx_online_mode.dart';
import '../../../core/yahoo/online_mode/yahoo_online_mode.dart';
import '../../../core/icloud/online_mode/icloud_online_mode.dart';

class HomeScreenQuickCleanButton extends StatefulWidget {
  final String labelQuery; // ex: 'category:promotions' ou nom du dossier
  final String? labelName; // ex: 'Promotions'
  const HomeScreenQuickCleanButton({
    super.key,
    required this.labelQuery,
    this.labelName,
  });

  @override
  State<HomeScreenQuickCleanButton> createState() => _HomeScreenQuickCleanButtonState();
}

class _HomeScreenQuickCleanButtonState extends State<HomeScreenQuickCleanButton> {
  bool _isProcessing = false;
  int _deletedCount = 0;
  String? _error;
  int _toDeleteCount = 0;
  bool _loadingCount = false;

  Future<void> _quickClean(UserProvider userProvider) async {
    setState(() { _isProcessing = true; _error = null; _deletedCount = 0; });
    try {
      final userType = userProvider.userType;
      List<String> allEmails = [];
      String? pageToken;
      switch (userType) {
        case UserType.gmail:
          final api = GmailApiService(userProvider.user!);
          do {
            final result = await api.getEmails(q: widget.labelQuery, maxResults: 500, pageToken: pageToken, includeMetadata: false);
            final messages = result['messages'] as List<dynamic>? ?? [];
            allEmails.addAll(messages.map((m) => m['id'] as String));
            pageToken = result['nextPageToken'] as String?;
          } while (pageToken != null);
          if (allEmails.isNotEmpty) await api.batchDeleteEmails(allEmails);
          break;
        case UserType.gmx:
          final api = GmxApiService(userProvider.gmxUser!);
          final result = await api.getEmails(q: widget.labelQuery, maxResults: 500, includeMetadata: false);
          final messages = result['messages'] as List<dynamic>? ?? [];
          allEmails.addAll(messages.map((m) => m['id'] as String));
          if (allEmails.isNotEmpty) await api.deleteMessages(allEmails);
          break;
        case UserType.yahoo:
          final api = YahooApiService(userProvider.yahooUser!);
          final result = await api.getEmails(q: widget.labelQuery, maxResults: 500, includeMetadata: false);
          final messages = result['messages'] as List<dynamic>? ?? [];
          allEmails.addAll(messages.map((m) => m['id'] as String));
          if (allEmails.isNotEmpty) await api.deleteMessages(allEmails);
          break;
        case UserType.icloud:
          final api = IcloudApiService(userProvider.icloudUser!);
          final result = await api.getEmails(q: widget.labelQuery, maxResults: 500, includeMetadata: false);
          final messages = result['messages'] as List<dynamic>? ?? [];
          allEmails.addAll(messages.map((m) => m['id'] as String));
          if (allEmails.isNotEmpty) await api.deleteMessages(allEmails);
          break;
      }
      setState(() { _isProcessing = false; _deletedCount = allEmails.length; });
    } catch (e) {
      setState(() { _isProcessing = false; _error = e.toString(); });
    }
  }

  Future<void> _loadToDeleteCount(UserProvider userProvider) async {
    setState(() { _loadingCount = true; });
    try {
      final userType = userProvider.userType;
      int count = 0;
      switch (userType) {
        case UserType.gmail:
          final api = GmailApiService(userProvider.user!);
          final result = await api.getEmails(q: widget.labelQuery, maxResults: 1, includeMetadata: false);
          count = result['resultSizeEstimate'] ?? 0;
          break;
        case UserType.gmx:
          final api = GmxApiService(userProvider.gmxUser!);
          final result = await api.getEmails(q: widget.labelQuery, maxResults: 1, includeMetadata: false);
          count = result['resultSizeEstimate'] ?? 0;
          break;
        case UserType.yahoo:
          final api = YahooApiService(userProvider.yahooUser!);
          final result = await api.getEmails(q: widget.labelQuery, maxResults: 1, includeMetadata: false);
          count = result['resultSizeEstimate'] ?? 0;
          break;
        case UserType.icloud:
          final api = IcloudApiService(userProvider.icloudUser!);
          final result = await api.getEmails(q: widget.labelQuery, maxResults: 1, includeMetadata: false);
          count = result['resultSizeEstimate'] ?? 0;
          break;
      }
      setState(() { _toDeleteCount = count; _loadingCount = false; });
    } catch (e) {
      setState(() { _toDeleteCount = 0; _loadingCount = false; });
    }
  }

  void _showConfirmDialog(BuildContext context, UserProvider userProvider) async {
    // Store context and l10n before async operation
    final l10n = AppLocalizations.of(context);
    final dialogContext = context;

    await _loadToDeleteCount(userProvider);
    if (!mounted) return;

    showDialog(
      context: dialogContext,
      builder: (ctx) => AlertDialog(
        title: Text(l10n.quickCleanConfirmTitle),
        content: _loadingCount
          ? const Center(child: CircularProgressIndicator())
          : Text(
              (_toDeleteCount > 0)
                ? l10n.quickCleanConfirmContent(widget.labelName ?? widget.labelQuery).replaceFirst('{count}', _toDeleteCount.toString())
                : l10n.quickCleanConfirmContent(widget.labelName ?? widget.labelQuery),
            ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(ctx).pop(),
            child: Text(l10n.cancel),
          ),
          TextButton(
            onPressed: _loadingCount || _toDeleteCount == 0 ? null : () {
              Navigator.of(ctx).pop();
              _quickClean(userProvider);
            },
            child: Text(l10n.ok),
          ),
        ],
      ),
    );
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    final userProvider = Provider.of<UserProvider>(context, listen: false);
    _loadToDeleteCount(userProvider);
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    final userProvider = Provider.of<UserProvider>(context, listen: false);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        ElevatedButton.icon(
          icon: const Icon(Icons.cleaning_services),
          label: _loadingCount
            ? const SizedBox(width: 20, height: 20, child: CircularProgressIndicator(strokeWidth: 2, color: Colors.white))
            : Text(
                l10n.quickCleanButton(widget.labelName ?? widget.labelQuery)
                  .replaceFirst('{count}', _toDeleteCount > 0 ? '($_toDeleteCount)' : ''),
              ),
          onPressed: _isProcessing || _loadingCount ? null : () => _showConfirmDialog(context, userProvider),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.orangeAccent,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(vertical: 16),
          ),
        ),
        if (_isProcessing)
          const Padding(
            padding: EdgeInsets.symmetric(vertical: 8),
            child: Center(child: CircularProgressIndicator()),
          ),
        if (_deletedCount > 0)
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Center(child: Text(l10n.quickCleanSuccess(_deletedCount), style: const TextStyle(color: Colors.green))),
          ),
        if (_error != null)
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Center(child: Text(_error!, style: const TextStyle(color: Colors.red))),
          ),
      ],
    );
  }
} 