import 'package:flutter/material.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import '../../../core/gmail/online_mode/gmail_online_mode.dart';

class HomeScreenQuickCleanButton extends StatefulWidget {
  final GoogleSignInAccount user;
  final String labelQuery; // ex: 'category:promotions'
  final String? labelName; // ex: 'Promotions'
  const HomeScreenQuickCleanButton({
    super.key,
    required this.user,
    required this.labelQuery,
    this.labelName,
  });

  @override
  State<HomeScreenQuickCleanButton> createState() => _HomeScreenQuickCleanButtonState();
}

class _HomeScreenQuickCleanButtonState extends State<HomeScreenQuickCleanButton> {
  bool _isProcessing = false;
  int _deletedCount = 0;
  String? _error;

  Future<void> _quickClean() async {
    setState(() { _isProcessing = true; _error = null; _deletedCount = 0; });
    try {
      final api = GmailApiService(widget.user);
      // Récupérer tous les emails de la catégorie
      final allEmails = <String>[];
      String? pageToken;
      do {
        final result = await api.getEmails(q: widget.labelQuery, maxResults: 500, pageToken: pageToken, includeMetadata: false);
        final messages = result['messages'] as List<dynamic>? ?? [];
        allEmails.addAll(messages.map((m) => m['id'] as String));
        pageToken = result['nextPageToken'] as String?;
      } while (pageToken != null);
      if (allEmails.isEmpty) {
        setState(() { _isProcessing = false; _deletedCount = 0; });
        return;
      }
      await api.batchDeleteEmails(allEmails);
      setState(() { _isProcessing = false; _deletedCount = allEmails.length; });
    } catch (e) {
      setState(() { _isProcessing = false; _error = e.toString(); });
    }
  }

  void _showConfirmDialog(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    showDialog(
      context: context,
      builder: (ctx) => AlertDialog(
        title: Text(l10n?.quickCleanConfirmTitle ?? 'Confirm Quick Clean'),
        content: Text(l10n?.quickCleanConfirmContent(widget.labelName ?? widget.labelQuery) ?? 'Delete all emails in this category?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(ctx).pop(),
            child: Text(l10n?.cancel ?? 'Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(ctx).pop();
              _quickClean();
            },
            child: Text(l10n?.ok ?? 'OK'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        ElevatedButton.icon(
          icon: const Icon(Icons.cleaning_services),
          label: Text(l10n?.quickCleanButton(widget.labelName ?? widget.labelQuery) ?? 'Quick Clean'),
          onPressed: _isProcessing ? null : () => _showConfirmDialog(context),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.orangeAccent,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(vertical: 16),
          ),
        ),
        if (_isProcessing)
          const Padding(
            padding: EdgeInsets.symmetric(vertical: 8),
            child: Center(child: CircularProgressIndicator()),
          ),
        if (_deletedCount > 0)
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Center(child: Text(l10n?.quickCleanSuccess(_deletedCount) ?? 'Deleted $_deletedCount emails', style: const TextStyle(color: Colors.green))),
          ),
        if (_error != null)
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Center(child: Text(_error!, style: const TextStyle(color: Colors.red))),
          ),
      ],
    );
  }
} 